[babel.checkers]
num_plurals = babel.messages.checkers:num_plurals
python_format = babel.messages.checkers:python_format

[babel.extractors]
ignore = babel.messages.extract:extract_nothing
javascript = babel.messages.extract:extract_javascript
python = babel.messages.extract:extract_python

[console_scripts]
pybabel = babel.messages.frontend:main

[distutils.commands]
compile_catalog = babel.messages.setuptools_frontend:compile_catalog
extract_messages = babel.messages.setuptools_frontend:extract_messages
init_catalog = babel.messages.setuptools_frontend:init_catalog
update_catalog = babel.messages.setuptools_frontend:update_catalog

[distutils.setup_keywords]
message_extractors = babel.messages.setuptools_frontend:check_message_extractors
