!!python/object:nerfstudio.engine.trainer.TrainerConfig
_target: !!python/name:nerfstudio.engine.trainer.Trainer ''
data: &id003 !!python/object/apply:pathlib.WindowsPath
- processed_data
experiment_name: processed_data
gradient_accumulation_steps: {}
load_checkpoint: null
load_config: null
load_dir: null
load_scheduler: true
load_step: null
log_gradients: false
logging: !!python/object:nerfstudio.configs.base_config.LoggingConfig
  local_writer: !!python/object:nerfstudio.configs.base_config.LocalWriterConfig
    _target: !!python/name:nerfstudio.utils.writer.LocalWriter ''
    enable: true
    max_log_size: 10
    stats_to_track: !!python/tuple
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Train Iter (time)
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Train Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Test PSNR
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Vis Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Test Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - ETA (time)
  max_buffer_size: 20
  profiler: basic
  relative_log_dir: !!python/object/apply:pathlib.WindowsPath []
  steps_per_log: 10
machine: !!python/object:nerfstudio.configs.base_config.MachineConfig
  device_type: cuda
  dist_url: auto
  machine_rank: 0
  num_devices: 1
  num_machines: 1
  seed: 42
max_num_iterations: 30000
method_name: nerfacto
mixed_precision: true
optimizers:
  camera_opt:
    optimizer: !!python/object:nerfstudio.engine.optimizers.AdamOptimizerConfig
      _target: &id001 !!python/name:torch.optim.adam.Adam ''
      eps: 1.0e-15
      lr: 0.001
      max_norm: null
      weight_decay: 0
    scheduler: !!python/object:nerfstudio.engine.schedulers.ExponentialDecaySchedulerConfig
      _target: &id002 !!python/name:nerfstudio.engine.schedulers.ExponentialDecayScheduler ''
      lr_final: 0.0001
      lr_pre_warmup: 1.0e-08
      max_steps: 5000
      ramp: cosine
      warmup_steps: 0
  fields:
    optimizer: !!python/object:nerfstudio.engine.optimizers.AdamOptimizerConfig
      _target: *id001
      eps: 1.0e-15
      lr: 0.01
      max_norm: null
      weight_decay: 0
    scheduler: !!python/object:nerfstudio.engine.schedulers.ExponentialDecaySchedulerConfig
      _target: *id002
      lr_final: 0.0001
      lr_pre_warmup: 1.0e-08
      max_steps: 200000
      ramp: cosine
      warmup_steps: 0
  proposal_networks:
    optimizer: !!python/object:nerfstudio.engine.optimizers.AdamOptimizerConfig
      _target: *id001
      eps: 1.0e-15
      lr: 0.01
      max_norm: null
      weight_decay: 0
    scheduler: !!python/object:nerfstudio.engine.schedulers.ExponentialDecaySchedulerConfig
      _target: *id002
      lr_final: 0.0001
      lr_pre_warmup: 1.0e-08
      max_steps: 200000
      ramp: cosine
      warmup_steps: 0
output_dir: !!python/object/apply:pathlib.WindowsPath
- outputs
pipeline: !!python/object:nerfstudio.pipelines.base_pipeline.VanillaPipelineConfig
  _target: !!python/name:nerfstudio.pipelines.base_pipeline.VanillaPipeline ''
  datamanager: !!python/object:nerfstudio.data.datamanagers.parallel_datamanager.ParallelDataManagerConfig
    _target: !!python/name:nerfstudio.data.datamanagers.parallel_datamanager.ParallelDataManager ''
    camera_optimizer: null
    camera_res_scale_factor: 1.0
    collate_fn: !!python/name:nerfstudio.data.utils.nerfstudio_collate.nerfstudio_collate ''
    data: *id003
    dataparser: !!python/object:nerfstudio.data.dataparsers.colmap_dataparser.ColmapDataParserConfig
      _target: !!python/name:nerfstudio.data.dataparsers.colmap_dataparser.ColmapDataParser ''
      assume_colmap_world_coordinate_convention: true
      auto_scale_poses: true
      center_method: poses
      colmap_path: !!python/object/apply:pathlib.WindowsPath
      - colmap
      - sparse
      - '0'
      data: !!python/object/apply:pathlib.WindowsPath []
      depth_unit_scale_factor: 0.001
      depths_path: null
      downscale_factor: null
      downscale_rounding_mode: floor
      eval_interval: 8
      eval_mode: interval
      images_path: !!python/object/apply:pathlib.WindowsPath
      - images
      load_3D_points: true
      masks_path: null
      max_2D_matches_per_3D_point: 0
      orientation_method: up
      scale_factor: 1.0
      scene_scale: 1.0
      train_split_fraction: 0.9
    eval_image_indices: !!python/tuple
    - 0
    eval_num_images_to_sample_from: -1
    eval_num_rays_per_batch: 4096
    eval_num_times_to_repeat_images: -1
    images_on_gpu: false
    masks_on_gpu: false
    max_thread_workers: null
    num_processes: 1
    patch_size: 1
    pixel_sampler: !!python/object:nerfstudio.data.pixel_samplers.PixelSamplerConfig
      _target: !!python/name:nerfstudio.data.pixel_samplers.PixelSampler ''
      fisheye_crop_radius: null
      ignore_mask: false
      is_equirectangular: false
      keep_full_image: false
      max_num_iterations: 100
      num_rays_per_batch: 4096
      rejection_sample_mask: true
    queue_size: 2
    train_num_images_to_sample_from: -1
    train_num_rays_per_batch: 4096
    train_num_times_to_repeat_images: -1
  model: !!python/object:nerfstudio.models.nerfacto.NerfactoModelConfig
    _target: !!python/name:nerfstudio.models.nerfacto.NerfactoModel ''
    appearance_embed_dim: 32
    average_init_density: 0.01
    background_color: last_sample
    base_res: 16
    camera_optimizer: !!python/object:nerfstudio.cameras.camera_optimizers.CameraOptimizerConfig
      _target: !!python/name:nerfstudio.cameras.camera_optimizers.CameraOptimizer ''
      mode: SO3xR3
      optimizer: null
      rot_l2_penalty: 0.001
      scheduler: null
      trans_l2_penalty: 0.01
    collider_params:
      far_plane: 6.0
      near_plane: 2.0
    disable_scene_contraction: false
    distortion_loss_mult: 0.002
    enable_collider: true
    eval_num_rays_per_chunk: 32768
    far_plane: 1000.0
    features_per_level: 2
    hidden_dim: 64
    hidden_dim_color: 64
    hidden_dim_transient: 64
    implementation: tcnn
    interlevel_loss_mult: 1.0
    log2_hashmap_size: 19
    loss_coefficients:
      rgb_loss_coarse: 1.0
      rgb_loss_fine: 1.0
    max_res: 2048
    near_plane: 0.05
    num_levels: 16
    num_nerf_samples_per_ray: 48
    num_proposal_iterations: 2
    num_proposal_samples_per_ray: !!python/tuple
    - 256
    - 96
    orientation_loss_mult: 0.0001
    pred_normal_loss_mult: 0.001
    predict_normals: true
    prompt: null
    proposal_initial_sampler: piecewise
    proposal_net_args_list:
    - hidden_dim: 16
      log2_hashmap_size: 17
      max_res: 128
      num_levels: 5
      use_linear: false
    - hidden_dim: 16
      log2_hashmap_size: 17
      max_res: 256
      num_levels: 5
      use_linear: false
    proposal_update_every: 5
    proposal_warmup: 5000
    proposal_weights_anneal_max_num_iters: 1000
    proposal_weights_anneal_slope: 10.0
    use_appearance_embedding: true
    use_average_appearance_embedding: true
    use_gradient_scaling: false
    use_proposal_weight_anneal: true
    use_same_proposal_network: false
    use_single_jitter: true
project_name: nerfstudio-project
prompt: null
relative_model_dir: !!python/object/apply:pathlib.WindowsPath
- nerfstudio_models
save_only_latest_checkpoint: true
start_paused: false
steps_per_eval_all_images: 25000
steps_per_eval_batch: 500
steps_per_eval_image: 500
steps_per_save: 2000
timestamp: 2025-07-04_123952
use_grad_scaler: false
viewer: !!python/object:nerfstudio.configs.base_config.ViewerConfig
  camera_frustum_scale: 0.1
  default_composite_depth: true
  image_format: jpeg
  jpeg_quality: 75
  make_share_url: false
  max_num_display_images: 512
  num_rays_per_chunk: 32768
  quit_on_train_completion: false
  relative_log_filename: viewer_log_filename.txt
  websocket_host: 0.0.0.0
  websocket_port: null
  websocket_port_default: 7007
vis: viewer
