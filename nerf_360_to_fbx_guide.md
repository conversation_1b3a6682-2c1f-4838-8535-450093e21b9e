# Complete Guide: Convert 360° Video to FBX using Nerfstudio

## Prerequisites

### Hardware Requirements
- **NVIDIA GPU with CUDA support** (minimum 8GB VRAM recommended)
- **16GB+ RAM** (32GB recommended for large datasets)
- **50GB+ free disk space** for processing

### Software Prerequisites
- Python 3.8 or higher
- CUDA 11.7 or 11.8
- FFmpeg
- COLMAP
- Blender (for FBX conversion)

## Step 1: Environment Setup

### Option A: Using Python venv (Recommended for Windows)
```bash
# Check Python version (needs 3.8+)
python --version

# Create virtual environment
python -m venv nerfstudio_env

# Activate environment (Windows)
nerfstudio_env\Scripts\activate

# Activate environment (Linux/Mac)
# source nerfstudio_env/bin/activate
```

### Option B: Using Conda (if you have it installed)
```bash
# Create a new conda environment
conda create -n nerfstudio python=3.9
conda activate nerfstudio

# Install CUDA toolkit
conda install -c conda-forge cudatoolkit=11.8
```

### Install PyTorch with CUDA
```bash
# Install PyTorch with CUDA 11.8 (works with both venv and conda)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Verify CUDA is available
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### Install tiny-cuda-nn
```bash
# Install tiny-cuda-nn (required for nerfstudio)
pip install ninja git+https://github.com/NVlabs/tiny-cuda-nn/#subdirectory=bindings/torch
```

### Install Nerfstudio
```bash
# Install the latest version of nerfstudio
pip install nerfstudio

# Verify installation
ns-train --help
```

### Install Additional Dependencies
```bash
# Install FFmpeg (Windows - using chocolatey)
# First install chocolatey if you don't have it: https://chocolatey.org/install
# choco install ffmpeg

# Or download FFmpeg manually from: https://ffmpeg.org/download.html
# Extract and add to PATH

# Install COLMAP (Windows)
# Download from: https://github.com/colmap/colmap/releases
# Or use pre-built binaries

# Alternative: Use pip for cross-platform installation
pip install opencv-python
pip install imageio
pip install pillow

# Note: You may need to install COLMAP separately
# For Windows: Download from GitHub releases
# For Ubuntu/Linux: sudo apt install colmap
```

## Step 2: Prepare Your 360° Video Data

### For Standard 360° Video
```bash
# Create project directory
mkdir my_360_project
cd my_360_project

# Create subdirectories
mkdir original_video frames processed_data outputs exports

# Place your 360° video in the original_video folder
# Example: original_video/my_360_video.mp4
```

### Extract Frames from 360° Video
```bash
# Extract frames at 2-3 fps (adjust based on video length and quality needed)
ffmpeg -i original_video/my_360_video.mp4 \
       -vf fps=2 \
       -qscale:v 2 \
       frames/frame_%06d.jpg

# For high-quality extraction
ffmpeg -i original_video/my_360_video.mp4 \
       -vf fps=3 \
       -qscale:v 1 \
       frames/frame_%06d.jpg
```

### For Stereoscopic 360° Video (if applicable)
```bash
# Split top-bottom stereoscopic format
# Extract left eye (top half)
ffmpeg -i original_video/stereo_360.mp4 \
       -vf "crop=iw:ih/2:0:0,fps=2" \
       -qscale:v 2 \
       frames_left/frame_%06d.jpg

# Extract right eye (bottom half) 
ffmpeg -i original_video/stereo_360.mp4 \
       -vf "crop=iw:ih/2:0:ih/2,fps=2" \
       -qscale:v 2 \
       frames_right/frame_%06d.jpg

# For this guide, we'll use left eye frames
# Copy left eye frames to main frames folder
cp frames_left/* frames/
```

## Step 3: Process Data for Nerfstudio

### Basic Processing for 360° Video
```bash
# Process extracted frames
ns-process-data video \
    --data frames/ \
    --output-dir processed_data/ \
    --feature-type superpoint \
    --matching-method superglue \
    --sfm-tool colmap \
    --camera-type equirectangular \
    --num-frames-target 150

# Alternative for faster processing (lower quality)
ns-process-data images \
    --data frames/ \
    --output-dir processed_data/ \
    --camera-type perspective
```

### Advanced 360° Processing
```bash
# For equirectangular 360° videos with better quality
ns-process-data video \
    --data frames/ \
    --output-dir processed_data/ \
    --camera-type equirectangular \
    --images-per-equirect 8 \
    --num-frames-target 200 \
    --crop-factor 0 0.2 0 0 \
    --feature-type superpoint \
    --matching-method superglue \
    --verbose
```

## Step 4: Train NeRF Model

### Basic Training
```bash
# Train nerfacto model with normal prediction (required for mesh export)
ns-train nerfacto \
    --data processed_data/ \
    --pipeline.model.predict-normals True \
    --output-dir outputs/

# Monitor training progress in web viewer (usually http://localhost:7007)
```

### High-Quality Training
```bash
# Extended training for better quality
ns-train nerfacto \
    --data processed_data/ \
    --pipeline.model.predict-normals True \
    --pipeline.model.background-color white \
    --max-num-iterations 30000 \
    --steps-per-eval-image 1000 \
    --steps-per-save 5000 \
    --output-dir outputs/
```

### Training with Multiple GPUs (if available)
```bash
# Multi-GPU training
export CUDA_VISIBLE_DEVICES=0,1
ns-train nerfacto \
    --data processed_data/ \
    --pipeline.model.predict-normals True \
    --machine.num-devices 2 \
    --pipeline.datamanager.train-num-rays-per-batch 4096 \
    --output-dir outputs/
```

## Step 5: Export 3D Mesh

### Find Your Trained Model
```bash
# List available trained models
ls outputs/

# Your config file will be something like:
# outputs/nerfacto/2024-XX-XX_XXXXXX/config.yml
```

### Export Using Poisson Surface Reconstruction (Highest Quality)
```bash
# Export high-quality mesh with Poisson reconstruction
ns-export poisson \
    --load-config outputs/nerfacto/[YOUR_TIMESTAMP]/config.yml \
    --output-dir exports/poisson_mesh/ \
    --target-num-faces 100000 \
    --num-pixels-per-side 2048 \
    --normal-method open3d \
    --depth-method depth

# This creates: exports/poisson_mesh/mesh.obj and mesh.mtl
```

### Alternative: TSDF Fusion (Works with any model)
```bash
# Export using TSDF fusion (more robust, works without normals)
ns-export tsdf \
    --load-config outputs/nerfacto/[YOUR_TIMESTAMP]/config.yml \
    --output-dir exports/tsdf_mesh/ \
    --resolution 512 \
    --batch-size 4096

# This creates: exports/tsdf_mesh/mesh.obj
```

### Export Point Cloud (Alternative)
```bash
# Export point cloud if mesh quality is poor
ns-export pointcloud \
    --load-config outputs/nerfacto/[YOUR_TIMESTAMP]/config.yml \
    --output-dir exports/pointcloud/ \
    --num-points 1000000 \
    --remove-outliers True

# This creates: exports/pointcloud/pointcloud.ply
```

## Step 6: Convert OBJ/PLY to FBX using Blender

### Install Blender
```bash
# Download and install Blender from https://www.blender.org/
# Add Blender to your PATH or note the installation directory
```

### Create Conversion Script

Create a file called `convert_to_fbx.py`:

```python
import bpy
import sys
import os

def clear_scene():
    """Clear all objects from the scene"""
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)

def convert_obj_to_fbx(input_path, output_path):
    """Convert OBJ file to FBX"""
    clear_scene()
    
    # Import OBJ file
    bpy.ops.import_scene.obj(
        filepath=input_path,
        axis_forward='-Z',
        axis_up='Y'
    )
    
    # Select all imported objects
    bpy.ops.object.select_all(action='SELECT')
    
    # Export as FBX
    bpy.ops.export_scene.fbx(
        filepath=output_path,
        axis_forward='-Z',
        axis_up='Y',
        use_selection=True,
        use_mesh_modifiers=True,
        mesh_smooth_type='FACE'
    )
    
    print(f"Converted {input_path} to {output_path}")

def convert_ply_to_fbx(input_path, output_path):
    """Convert PLY file to FBX"""
    clear_scene()
    
    # Import PLY file
    bpy.ops.import_mesh.ply(filepath=input_path)
    
    # Select all imported objects
    bpy.ops.object.select_all(action='SELECT')
    
    # Export as FBX
    bpy.ops.export_scene.fbx(
        filepath=output_path,
        axis_forward='-Z',
        axis_up='Y',
        use_selection=True,
        use_mesh_modifiers=True,
        mesh_smooth_type='FACE'
    )
    
    print(f"Converted {input_path} to {output_path}")

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("Usage: blender --background --python convert_to_fbx.py -- input_file output_file")
        sys.exit(1)
    
    argv = sys.argv
    argv = argv[argv.index("--") + 1:]
    
    input_file = argv[0]
    output_file = argv[1]
    
    # Determine file type and convert
    if input_file.lower().endswith('.obj'):
        convert_obj_to_fbx(input_file, output_file)
    elif input_file.lower().endswith('.ply'):
        convert_ply_to_fbx(input_file, output_file)
    else:
        print("Unsupported file format. Use .obj or .ply files.")
        sys.exit(1)
```

### Convert Your Mesh to FBX

#### For OBJ files:
```bash
# Convert OBJ mesh to FBX
blender --background --python convert_to_fbx.py -- \
    exports/poisson_mesh/mesh.obj \
    exports/final_model.fbx

# If Blender is not in PATH, use full path:
# /path/to/blender --background --python convert_to_fbx.py -- exports/poisson_mesh/mesh.obj exports/final_model.fbx
```

#### For PLY files:
```bash
# Convert PLY pointcloud to FBX
blender --background --python convert_to_fbx.py -- \
    exports/pointcloud/pointcloud.ply \
    exports/final_pointcloud.fbx
```

### Manual Cleanup in Blender (Optional)
```bash
# Open Blender GUI for manual cleanup
blender exports/final_model.fbx
```

Manual cleanup steps:
1. Remove unwanted geometry/artifacts
2. Improve mesh topology
3. Apply materials and textures
4. Scale and position the model correctly
5. Re-export as FBX with desired settings

## Step 7: Optimization and Quality Improvement

### Mesh Cleanup Script for Blender

Create `cleanup_mesh.py`:

```python
import bpy
import bmesh

def cleanup_mesh():
    """Clean up the imported mesh"""
    # Switch to Edit mode
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Create bmesh instance
    bm = bmesh.from_mesh(bpy.context.active_object.data)
    
    # Remove duplicate vertices
    bmesh.ops.remove_doubles(bm, verts=bm.verts, dist=0.001)
    
    # Remove degenerate faces
    bmesh.ops.dissolve_degenerate(bm, edges=bm.edges, dist=0.001)
    
    # Fill holes
    bmesh.ops.holes_fill(bm, edges=bm.edges)
    
    # Smooth normals
    bmesh.ops.smooth_vert(bm, verts=bm.verts, factor=0.5, repeat=2)
    
    # Update mesh
    bm.to_mesh(bpy.context.active_object.data)
    bm.free()
    
    # Switch back to Object mode
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Recalculate normals
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.normals_make_consistent(inside=False)
    bpy.ops.object.mode_set(mode='OBJECT')

# Apply cleanup to active object
if bpy.context.active_object:
    cleanup_mesh()
```

### Complete Conversion with Cleanup
```bash
# Create advanced conversion script
cat > advanced_convert.py << 'EOF'
import bpy
import sys

# Clear scene
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Get command line arguments
argv = sys.argv[sys.argv.index("--") + 1:]
input_file = argv[0]
output_file = argv[1]

# Import mesh
if input_file.endswith('.obj'):
    bpy.ops.import_scene.obj(filepath=input_file)
elif input_file.endswith('.ply'):
    bpy.ops.import_mesh.ply(filepath=input_file)

# Select the imported object
bpy.ops.object.select_all(action='SELECT')
obj = bpy.context.active_object

if obj:
    # Basic cleanup
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.remove_doubles(threshold=0.001)
    bpy.ops.mesh.fill_holes()
    bpy.ops.mesh.normals_make_consistent(inside=False)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Set proper scale (adjust as needed)
    obj.scale = (1, 1, 1)
    
    # Apply transforms
    bpy.ops.object.transform_apply(location=True, rotation=True, scale=True)

# Export as FBX
bpy.ops.export_scene.fbx(
    filepath=output_file,
    axis_forward='-Z',
    axis_up='Y',
    use_selection=True,
    use_mesh_modifiers=True,
    mesh_smooth_type='FACE',
    use_tspace=True
)
EOF

# Run advanced conversion
blender --background --python advanced_convert.py -- \
    exports/poisson_mesh/mesh.obj \
    exports/final_cleaned_model.fbx
```

## Troubleshooting

### Common Issues and Solutions

1. **CUDA Out of Memory**
   ```bash
   # Reduce batch size and image resolution
   ns-train nerfacto --data processed_data/ \
       --pipeline.datamanager.train-num-rays-per-batch 2048 \
       --pipeline.model.predict-normals True
   ```

2. **Poor Mesh Quality**
   ```bash
   # Try different export methods
   ns-export tsdf --load-config config.yml --resolution 1024
   # Or increase Poisson parameters
   ns-export poisson --target-num-faces 200000 --num-pixels-per-side 4096
   ```

3. **Missing Textures in FBX**
   - Ensure MTL file is in same directory as OBJ
   - Use Blender's material nodes to rebuild textures manually

4. **Large File Sizes**
   ```bash
   # Reduce mesh complexity
   ns-export poisson --target-num-faces 50000
   ```

## Alternative Tools and Methods

### Using Instant-NGP for Faster Training
```bash
# Install instant-ngp (requires building from source)
git clone --recursive https://github.com/NVlabs/instant-ngp
cd instant-ngp
cmake . -B build
cmake --build build --config RelWithDebInfo -j

# Convert data for instant-ngp
python scripts/colmap2nerf.py --video_in original_video/my_360_video.mp4 --video_fps 2 --run_colmap --aabb_scale 16
```

### Using 360 Gaussian Splatting
```bash
# For potentially better 360° results
git clone --recursive https://github.com/inuex35/360-gaussian-splatting
cd 360-gaussian-splatting
pip install submodules/diff-gaussian-rasterization submodules/simple-knn plyfile pyproj

# Train model
python train.py --source_path your_360_data/ --model_path output/ --eval --resolution 2
```

## Expected Results

- **Training Time**: 2-6 hours depending on GPU and dataset size
- **Mesh Quality**: Good for visualization, may need cleanup for professional use
- **File Sizes**: FBX files typically 10-100MB depending on complexity
- **Applications**: VR, AR, game development, 3D printing (with post-processing)

## Next Steps

1. Import FBX into your target application (Unity, Unreal Engine, etc.)
2. Apply materials and lighting
3. Optimize for real-time rendering if needed
4. Consider UV unwrapping for better texture mapping

This pipeline provides a complete workflow from 360° video to FBX format, suitable for most 3D applications and game engines.